import { useMemo, useCallback } from 'react';
import { useDashboardData } from '@/hooks/useDashboardData';
import { useAuth } from '@/hooks/useAuth';
import { generateAnalyticsData, TimeRange } from '@/lib/utils/analytics';
import { ProjectAnalyticsData } from '@/lib/types/dashboard';
import { Project, User, Document, ProjectMilestone, Notification } from '@/lib/types';

/**
 * Configuration options for the useAnalytics hook
 */
interface UseAnalyticsOptions {
  /** Time range for analytics calculations */
  timeRange?: TimeRange;
  /** Enable/disable real-time updates */
  enableRealTime?: boolean;
  /** Custom filters for data */
  filters?: {
    projectStatus?: string;
    userRole?: string;
    searchTerm?: string;
  };
  /** Include additional analytics beyond basic project analytics */
  includeExtendedAnalytics?: boolean;
}

/**
 * Extended analytics data that includes comprehensive insights
 */
interface ExtendedAnalyticsData extends ProjectAnalyticsData {
  // Document analytics
  documentAnalytics: {
    totalDocuments: number;
    documentsByStatus: Record<string, number>;
    averageDocumentsPerProject: number;
    documentsCompletionRate: number;
  };
  
  // Milestone analytics
  milestoneAnalytics: {
    totalMilestones: number;
    milestonesByStatus: Record<string, number>;
    averageMilestonesPerProject: number;
    milestonesCompletionRate: number;
    overdueMilestones: number;
  };
  
  // User engagement analytics
  userEngagement: {
    activeUsers: number;
    totalUsers: number;
    usersByRole: Record<string, number>;
    averageProjectsPerUser: number;
  };
  
  // Notification analytics
  notificationAnalytics: {
    totalNotifications: number;
    unreadNotifications: number;
    notificationsByType: Record<string, number>;
  };
  
  // Performance metrics
  performanceMetrics: {
    dataFreshness: Date;
    queryPerformance: {
      usersQueryTime: number;
      projectsQueryTime: number;
      totalLoadTime: number;
    };
  };
}

/**
 * Result of the useAnalytics hook
 */
interface UseAnalyticsResult {
  // Core analytics data
  analytics: ExtendedAnalyticsData | null;
  
  // Loading and error states
  isLoading: boolean;
  error: string | null;
  
  // Data freshness indicators
  isFetching: boolean;
  isRefetching: boolean;
  lastUpdated: Date | null;
  
  // Actions
  refreshAnalytics: () => Promise<void>;
  exportAnalytics: (format: 'csv' | 'json') => void;
  
  // Time range management
  timeRange: TimeRange;
  setTimeRange: (range: TimeRange) => void;
  
  // Raw data access for custom analytics
  rawData: {
    projects: Project[];
    users: User[];
    supervisors: User[];
  };
}

/**
 * Comprehensive analytics hook that provides real-time analytics data
 * using the enhanced dashboard data hook system
 */
export function useAnalytics(options: UseAnalyticsOptions = {}): UseAnalyticsResult {
  const {
    timeRange: initialTimeRange = 'month',
    enableRealTime = true,
    filters = {},
    includeExtendedAnalytics = true
  } = options;

  const { user: currentUser } = useAuth();
  
  // Get comprehensive dashboard data
  const dashboardData = useDashboardData({
    filters: {
      searchTerm: filters.searchTerm,
      projectStatus: filters.projectStatus as any,
      userRole: filters.userRole as any,
      sortBy: 'createdAt',
      sortDirection: 'desc'
    }
  });

  const {
    users,
    projects,
    supervisors,
    loading,
    error,
    isFetching,
    isRefetching,
    refetch
  } = dashboardData;

  // Time range state management
  const [timeRange, setTimeRange] = useMemo(() => {
    let currentRange = initialTimeRange;
    return [
      currentRange,
      (newRange: TimeRange) => {
        currentRange = newRange;
      }
    ] as const;
  }, [initialTimeRange]);

  // Generate comprehensive analytics data
  const analytics = useMemo((): ExtendedAnalyticsData | null => {
    if (!projects || !users || projects.length === 0) {
      return null;
    }

    // Generate base analytics using the existing analytics module
    const baseAnalytics = generateAnalyticsData(projects, supervisors, timeRange);

    if (!includeExtendedAnalytics) {
      return baseAnalytics as ExtendedAnalyticsData;
    }

    // For now, we'll use the documents and milestones that are already included in projects
    // This avoids the hook calling issue and uses the associated data that's already fetched
    const allDocuments: Document[] = [];
    const allMilestones: ProjectMilestone[] = [];

    projects.forEach(project => {
      // Use documents from project if available (associated data)
      if (project.documents && Array.isArray(project.documents)) {
        allDocuments.push(...project.documents);
      }

      // Use milestones from project if available (associated data)
      if (project.milestones && Array.isArray(project.milestones)) {
        allMilestones.push(...project.milestones);
      }
    });

    const documentAnalytics = {
      totalDocuments: allDocuments.length,
      documentsByStatus: allDocuments.reduce((acc, doc) => {
        acc[doc.status] = (acc[doc.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      averageDocumentsPerProject: projects.length > 0 ? allDocuments.length / projects.length : 0,
      documentsCompletionRate: allDocuments.length > 0
        ? (allDocuments.filter(doc => doc.status === 'approved').length / allDocuments.length) * 100
        : 0
    };

    const now = new Date();
    const overdueMilestones = allMilestones.filter(milestone =>
      milestone.dueDate && new Date(milestone.dueDate) < now && !milestone.completed
    ).length;

    const milestoneAnalytics = {
      totalMilestones: allMilestones.length,
      milestonesByStatus: allMilestones.reduce((acc, milestone) => {
        const status = milestone.completed ? 'completed' : 'pending';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      averageMilestonesPerProject: projects.length > 0 ? allMilestones.length / projects.length : 0,
      milestonesCompletionRate: allMilestones.length > 0
        ? (allMilestones.filter(milestone => milestone.completed).length / allMilestones.length) * 100
        : 0,
      overdueMilestones
    };

    // Calculate user engagement analytics
    const userEngagement = {
      activeUsers: users.filter(user => {
        // Consider a user active if they have projects or recent activity
        const userProjects = projects.filter(p => 
          p.studentId === user.id || (p.supervisorIds && p.supervisorIds.includes(user.id))
        );
        return userProjects.length > 0;
      }).length,
      totalUsers: users.length,
      usersByRole: users.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      averageProjectsPerUser: users.length > 0 ? projects.length / users.length : 0
    };

    // Calculate notification analytics (if current user is available)
    let notificationAnalytics = {
      totalNotifications: 0,
      unreadNotifications: 0,
      notificationsByType: {} as Record<string, number>
    };

    if (currentUser) {
      const userNotifications = getUserNotifications(currentUser.id);
      if (userNotifications.notifications) {
        const notifications = userNotifications.notifications;
        notificationAnalytics = {
          totalNotifications: notifications.length,
          unreadNotifications: notifications.filter(n => !n.read).length,
          notificationsByType: notifications.reduce((acc, notification) => {
            const type = notification.type || 'general';
            acc[type] = (acc[type] || 0) + 1;
            return acc;
          }, {} as Record<string, number>)
        };
      }
    }

    // Performance metrics
    const performanceMetrics = {
      dataFreshness: new Date(),
      queryPerformance: {
        usersQueryTime: 0, // Would need to implement timing
        projectsQueryTime: 0, // Would need to implement timing
        totalLoadTime: 0 // Would need to implement timing
      }
    };

    return {
      ...baseAnalytics,
      documentAnalytics,
      milestoneAnalytics,
      userEngagement,
      notificationAnalytics,
      performanceMetrics
    };
  }, [projects, users, supervisors, timeRange, includeExtendedAnalytics, currentUser]);

  // Refresh analytics data
  const refreshAnalytics = useCallback(async () => {
    await refetch();
  }, [refetch]);

  // Export analytics data
  const exportAnalytics = useCallback((format: 'csv' | 'json') => {
    if (!analytics) return;

    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `analytics-${timeRange}-${timestamp}`;

    if (format === 'json') {
      const dataStr = JSON.stringify(analytics, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${filename}.json`;
      link.click();
      URL.revokeObjectURL(url);
    } else {
      // CSV export logic would go here
      console.log('CSV export not implemented yet');
    }
  }, [analytics, timeRange]);

  return {
    analytics,
    isLoading: loading,
    error,
    isFetching,
    isRefetching,
    lastUpdated: analytics?.performanceMetrics.dataFreshness || null,
    refreshAnalytics,
    exportAnalytics,
    timeRange,
    setTimeRange,
    rawData: {
      projects: projects || [],
      users: users || [],
      supervisors: supervisors || []
    }
  };
}
